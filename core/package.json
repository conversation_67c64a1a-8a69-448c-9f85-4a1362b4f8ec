{"name": "@topwrite/core", "version": "1.0.41", "scripts": {"build": "rollup -c --environment NODE_ENV:production", "build:dev": "rollup -c", "watch": "rollup -c -w", "prepack": "npm run build", "test": "mocha"}, "main": "dist/index.js", "types": "types/index.d.ts", "files": ["dist", "schema", "types"], "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "license": "ISC", "dependencies": {"@babel/runtime": "^7.11.2", "@types/json-schema": "^7.0.7", "@types/lodash": "^4.14.161", "@types/mdast": "^3.0.7", "@types/mdurl": "^1.0.2", "@types/unist": "^2.0.3", "ajv": "^6.7.0", "assert": "^2.0.0", "axios": "^0.26.1", "crypto-js": "^4.0.0", "js-yaml": "^4.1.0", "mdast-util-directive": "^2.2.0", "mdast-util-from-markdown": "^1.0.0", "mdast-util-to-hast": "^12.0.0", "mdast-util-to-markdown": "^1.1.1", "mdurl": "^1.0.1", "micromark": "^3.0.3", "micromark-core-commonmark": "^1.0.5", "micromark-factory-destination": "^1.0.0", "micromark-factory-label": "^1.0.2", "micromark-factory-space": "^1.0.0", "micromark-factory-title": "^1.0.2", "micromark-factory-whitespace": "^1.0.0", "micromark-util-character": "^1.1.0", "micromark-util-normalize-identifier": "^1.0.0", "micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.2", "remark-breaks": "^3.0.0", "remark-custom-heading-id": "^1.0.0", "remark-directive": "^2.0.0", "remark-frontmatter": "^4.0.0", "remark-gfm": "^3.0.1", "remark-parse": "^10.0.0", "retry-axios": "^2.6.0", "unified": "^10.1.0", "unist-util-is": "^5.1.1", "unist-util-select": "^5.0.0", "unist-util-visit": "^4.0.0"}, "peerDependencies": {"lodash": "^4.17.15"}, "devDependencies": {"@babel/core": "^7.13.8", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.13.9", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "@types/crypto-js": "^4.0.1", "@types/fs-extra": "^9.0.4", "@types/isomorphic-fetch": "^0.0.35", "@types/isomorphic-form-data": "^2.0.0", "@types/js-yaml": "^4.0.1", "@types/mocha": "^9.0.0", "@types/node": "^16.4.13", "builtin-modules": "^3.2.0", "lodash": "^4.17.20", "mocha": "^9.0.3", "rollup": "^2.26.11", "rollup-plugin-node-externals": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.1", "ts-node": "^10.0.0", "typescript": "^5.3.3"}}