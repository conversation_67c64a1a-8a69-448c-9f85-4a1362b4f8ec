{"name": "@topwrite/editor", "version": "1.0.87", "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "types": "types/index.d.ts", "main": "dist/index.js", "exports": {"./plate": "./types/plate.d.ts"}, "license": "ISC", "files": ["dist", "types"], "scripts": {"prebuild": "rimraf dist types", "build": "webpack --progress", "build:dev": "npm run build --env dev", "serve": "webpack serve --env dev", "serve:prod": "npm run serve --env prod", "prepack": "npm run build"}, "dependencies": {"@topthink/json-form": "^1.0.65", "@topwrite/common": "^1.0.66", "@types/lodash": "^4.14.166", "@types/mdast": "^3.0.3", "@types/node": "^16.4.13", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/unist": "^2.0.3", "dayjs": "^1.10.4", "eventemitter3": "^4.0.7", "immer": "^9.0.6", "nanoid": "^3.1.25", "rc-notification": "^4.5.2", "react-bootstrap": "^2.9.1", "react-fast-compare": "^3.2.1", "react-textarea-autosize": "^8.5.9", "remark-stringify": "^10.0.0", "slate": "^0.100.0", "slate-history": "^0.100.0", "slate-react": "^0.100.0"}, "devDependencies": {"@emotion/react": "^11.6.0", "@react-hook/event": "^1.2.6", "@react-hook/hover": "^4.0.0", "@reactour/tour": "^3.3.0", "@topthink/webpack-config-plugin": "^1.0.16", "@types/diff": "^5.0.0", "@types/is-hotkey": "^0.1.1", "@types/js-yaml": "^4.0.5", "@types/json-schema": "^7.0.7", "@types/mime": "^2.0.3", "@types/rusha": "^0.8.2", "ajv": "^6.7.0", "ajv-i18n": "^3.6.0", "allotment": "^1.20.4", "bootstrap": "^5.2.3", "buffer": "^6.0.3", "classnames": "^2.2.6", "diff": "^5.0.0", "filenamify": "^5.0.1", "filesize": "^8.0.7", "idb-keyval": "^5.0.4", "is-hotkey": "^0.2.0", "js-yaml": "^4.1.0", "mdast-util-to-string": "^3.1.0", "micromark-util-character": "^1.1.0", "mime": "^2.4.6", "mime-match": "^1.0.2", "mockjs": "^1.1.0", "p-map": "^5.3.0", "prettier": "^2.6.2", "process": "^0.11.10", "re-resizable": "^6.9.9", "react-bootstrap-typeahead": "^6.0.0-rc.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-icons": "4.8.0", "react-infinite-scroll-component": "^6.0.0", "rehype-parse": "^8.0.2", "rehype-remark": "^9.1.0", "rimraf": "^3.0.2", "rusha": "^0.8.13", "scroll-sync-react": "^1.1.8", "socket.io-client": "^4.0.1", "stringify-entities": "^4.0.3", "typescript": "^5.3.3", "url": "^0.11.0", "use-state-if-mounted": "^1.0.4", "webpack": "^5.36.2", "webpack-cli": "^4.7.0", "webpack-dev-server": "^4.7.3"}}