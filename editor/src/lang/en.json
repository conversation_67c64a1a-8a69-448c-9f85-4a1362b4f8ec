{"header.untitled": "Untitled", "body.reload": "The document directory structure has changed and needs to be reloaded!", "active-bar.catalog": "Catalog", "active-bar.files": "Files", "active-bar.theme": "Theme", "active-bar.extension": "Extensions", "active-bar.commit": "Commit", "active-bar.commit.change": "Changes", "active-bar.history": "History", "active-bar.release": "Release", "active-bar.settings": "Management", "active-bar.settings.readme": "Document Overview", "active-bar.settings.config": "Document Settings", "active-bar.settings.style": "Style Settings", "active-bar.settings.config.base": "Basic Settings", "active-bar.settings.config.base.language": "Language", "active-bar.settings.config.structure": "Path Settings", "active-bar.settings.config.structure.root": "Document Root Directory", "active-bar.settings.config.structure.root.placeholder": "Enter the document root directory path relative to the repository root", "active-bar.settings.config.structure.root.description": "Set the document path in the git repository. <PERSON><PERSON>ult is empty for repository root. Do not set unless necessary", "active-bar.settings.config.pdf": "PDF Settings", "active-bar.settings.config.pdf.font_family": "Font Family", "active-bar.settings.config.pdf.margin": "Margins (unit: pt)", "active-bar.settings.config.pdf.margin.right": "Right", "active-bar.settings.config.pdf.margin.left": "Left", "active-bar.settings.config.pdf.margin.top": "Top", "active-bar.settings.config.pdf.margin.bottom": "Bottom", "active-bar.settings.config.pdf.paper_size": "Paper Size", "active-bar.settings.config.pdf.chapter_mark": "Chapter Mark", "active-bar.settings.config.pdf.page_breaks_before": "Insert page breaks before specified elements, XPath expression, \"/\" disables", "active-bar.settings.config.pdf.page_numbers": "Show Page Numbers", "active-bar.settings.config.release": "Release Settings", "active-bar.settings.config.release.path_encode": "Encode chapter paths (default converts Chinese to Pinyin)", "active-bar.files.search": "Search", "active-bar.files.search.empty": "No related files found", "active-bar.files.replace": "Replace", "side-bar.untitled": "Untitled Group", "side-bar.rename": "<PERSON><PERSON>", "side-bar.delete": "Delete", "side-bar.new_name": "New Filename", "side-bar.plugin.extension": "Installed", "side-bar.plugin.extension.title": "Recommended Extensions", "side-bar.plugin.extension.placeholder": "Search Extensions", "side-bar.plugin.theme": "Current Theme", "side-bar.plugin.theme.preview": "Preview", "side-bar.plugin.theme.title": "Recommended Themes", "side-bar.plugin.theme.placeholder": "Search Themes", "status-bar.sync": "Sync Changes", "status-bar.preview": "Full Preview", "status-bar.feedback": "Online Support", "catalog.new_part": "New Group", "catalog.edit_part": "Edit Group", "catalog.new_article": "New Chapter", "catalog.import_article": "Import Chapter", "catalog.import_desc": "Click the format below to import. It is recommended to commit all changes before importing to facilitate canceling this import", "catalog.import_failed": "Import Failed", "catalog.import_as_single": "Import as Single Chapter", "catalog.import_start": "Start Import", "catalog.import_cancel": "Cancel Import", "catalog.import_website": "Static Site", "catalog.import_website_series": "VitePress, docsify, etc.", "catalog.import_website.placeholder": "Please enter the webpage address", "catalog.import_website.message": "Supports static site generators like VitePress, docsify, Docusaurus, etc.", "catalog.import_independent": "Independent Directory", "catalog.import_independent.message": "When enabled, imported files will be placed in an independent directory to avoid conflicts with existing files", "catalog.edit_article": "Edit Chapter", "catalog.article_title": "Chapter Title", "catalog.article_ref": "Chapter Path", "catalog.edit": "Edit", "catalog.part_name": "Group Name", "confirm.delete": "Are you sure you want to delete?", "modal.ok": "OK", "modal.apply": "Apply", "modal.confirm": "Confirm", "modal.cancel": "Cancel", "modal.insert": "Insert", "modal.select": "Select", "modal.close": "Close", "commit.empty": "No changes", "commit.reset_all": "Discard All Changes", "commit.reset": "Discard Changes", "commit.reset_title": "Discard changes to {filename}?", "commit.reset_message": "You will lose all changes made to this file. This action cannot be undone.", "commit.reset_all_title": "Discard All Changes?", "commit.reset_all_message": "You will discard all uncommitted changes made in this project. This action cannot be undone.", "commit.message": "Commit Message", "commit.release": "Create Release Task", "commit.hide": "Collapse", "commit.submit": "Commit", "commit.submit_with_release": "Commit and Release", "commit.releasing": "There are ongoing release tasks", "diff.deleted": "Deleted", "editor.placeholder": "Please enter content...", "editor.markdown.error": "Markdown format parsing failed", "editor.markdown.switch": "Switch to Markdown mode editing", "editor.tool.heading1": "Heading 1", "editor.tool.heading2": "Heading 2", "editor.tool.heading3": "Heading 3", "editor.tool.heading4": "Heading 4", "editor.tool.strong": "Bold", "editor.tool.emphasis": "Italic", "editor.tool.delete": "Strikethrough", "editor.tool.code": "Code", "editor.tool.format": "Format", "editor.tool.align.left": "Left Align", "editor.tool.align.center": "Center Align", "editor.tool.align.right": "Right Align", "editor.tool.list.ordered": "Ordered List", "editor.tool.list.bullet": "Unordered List", "editor.tool.list.task": "Task List", "editor.tool.link": "Hyperlink", "editor.tool.link.insert": "Insert Link", "editor.tool.link.edit": "Set Link", "editor.tool.link.url": "Link", "editor.tool.link.url.placeholder": "Enter chapter title or external link", "editor.tool.link.text": "Text", "editor.tool.link.text.placeholder": "Add description", "editor.tool.table": "Table", "editor.tool.asset.upload": "Upload", "editor.tool.asset.browse": "Browse", "editor.tool.asset.browse.title": "File Management", "editor.tool.image": "Image", "editor.tool.image.conflict": "Detected existing image file with the same name. Overwrite? Click \"Cancel\" to auto-rename", "editor.tool.image.invalid_name": "Invalid filename", "editor.tool.image.only_image": "Only image files can be uploaded", "editor.tool.image.placeholder": "Please enter image address", "editor.tool.image.title": "Insert Image", "editor.tool.image.preview": "Image Preview Area", "editor.tool.video": "Video", "editor.tool.video.title": "Insert Video", "editor.tool.video.placeholder": "Please enter video address", "editor.tool.audio": "Audio", "editor.tool.audio.title": "Insert Audio", "editor.tool.audio.placeholder": "Please enter audio address", "editor.tool.attachment": "Attachment", "editor.tool.attachment.title": "Insert Attachment", "editor.tool.attachment.placeholder": "Please enter attachment address", "editor.tool.attachment.name": "Filename", "editor.tool.attachment.size": "File Size", "editor.tool.thematic-break": "Divider", "editor.tool.blockquote": "Quote", "editor.tool.markdown": "Source Code Mode", "editor.tool.fullscreen": "Fullscreen", "editor.tool.history": "History", "editor.tool.history.redo": "Redo", "editor.tool.history.undo": "Undo", "editor.tool.history.title": "Modification History", "editor.tool.history.revert": "Restore to this version", "editor.tool.history.revert.confirm": "Are you sure you want to replace the current editing area content with this version?", "editor.tool.history.saved": "Document auto-saved", "editor.toolbar.edit": "Edit", "editor.toolbar.delete": "Delete", "editor.toolbar.table.insert_row": "Add Row", "editor.toolbar.table.insert_column": "Add Column", "editor.toolbar.table.remove_row": "Delete Current Row", "editor.toolbar.table.remove_column": "Delete Current Column", "editor.toolbar.image.original": "Original Size", "editor.toolbar.image.full": "Full Width", "editor.toolbar.link.open": "Open External Link", "editor.menu.delete": "Delete", "editor.menu.tooltip": "Click to show menu", "editor.workspace.binary.message": "This file is a binary file or uses unsupported text encoding and cannot be displayed in the editor.", "editor.workspace.binary.confirm": "Do you still want to open it?", "editor.frontmatter.title": "Page Title", "editor.frontmatter.keywords": "Keywords", "editor.frontmatter.description": "Page Description", "release.empty": "No release logs", "release.message": "Release Notes", "release.new": "Release Document", "release.pending": "Queued", "release.running": "Running", "release.succeed": "Success", "release.failed": "Failed", "release.unknown": "Unknown", "release.retry": "Retry", "release.pack": "Package", "release.download": "Download", "release.denied": "No Permission", "data.empty": "No data", "plugin.readme": "Introduction", "plugin.install": "Install", "plugin.uninstall": "Uninstall", "plugin.uninstall.theme": "Theme plugins cannot be uninstalled. Please install another theme plugin to replace it", "plugin.reload": "Requires Reload", "plugin.trial.button": "Trial", "plugin.trial.message": "In Trial", "plugin.buy.button": "Purchase", "plugin.buy.result.title": "Payment Result", "plugin.buy.result.message": "Please complete payment in the newly opened page", "plugin.buy.result.ok": "Payment Successful", "plugin.buy.result.error": "Payment not completed or result not returned. Please try again later", "plugin.disabled": "This plugin is not authorized and has been disabled", "plugin.trailing": "This plugin is in trial, expires on {date}", "plugin.expired": "This plugin trial has expired. Please purchase a license", "plugin.theme": "Theme", "plugin.extension": "Extension", "plugin.paid": "Paid", "plugin.theme.setting.title": "Theme Settings", "plugin.theme.setting.common": "Common Settings", "plugin.theme.setting.common.description": "Common settings are basic functionality provided by the system. Different themes have varying levels of support", "plugin.theme.setting.default_mode": "Default Mode", "plugin.theme.setting.default_mode.light": "Light", "plugin.theme.setting.default_mode.dark": "Dark", "plugin.theme.setting.primary_color": "Primary Color", "plugin.theme.setting.expand_level": "Default Expanded Directory Level", "plugin.theme.setting.expand_level.description": "0 means not expanded, 1 means expand first level, and so on. -1 means expand all", "plugin.theme.setting.navs": "Navigation Settings", "plugin.theme.setting.navs.title": "Text", "plugin.theme.setting.navs.url": "Link", "plugin.theme.setting.navs.url.description": "External links should start with http. Internal links can directly use filename, e.g., default.md", "plugin.theme.setting.current": "Current Theme", "plugin.extension.setting.title": "Extension Settings", "merge-tool.header": "You need to resolve some conflicts", "merge-tool.title": "{nums} conflict files to resolve", "merge-tool.empty": "Please select a file on the left to resolve conflicts", "merge-tool.theirs": "Final Version (Based on Others' Version)", "merge-tool.ours": "My Version", "merge-tool.pick": "Use", "merge-tool.reset": "Reset", "merge-tool.save": "Save", "merge-tool.use_theirs": "Are you sure to reset the final version to \"Others' Version\"?", "merge-tool.use_ours": "Are you sure to use \"My Version\" as the final version?", "diff-viewer.collapse": "This difference has been collapsed.", "diff-viewer.expand": "Click to expand.", "theme.dark": "Dark", "theme.light": "Light", "theme.auto": "Follow System", "tour.app.catalog": "For first-time editing, you need to create chapters first and then create content, or link other documents to the current document by creating groups", "tour.app.commit": "After editing content, you can click the <strong>Commit</strong> button in the commit bar to sync changes, or choose <strong>Commit and Release</strong> to publish simultaneously. Content changes need to be published to update reading", "tour.app.plugin": "If you need to change the reading theme or install other functional plugins, you can click the extensions bar to install. After installing plugins or themes, you need to republish for them to take effect", "tour.editor.tools": "Various content creation can be accomplished through the top toolbar", "tour.editor.history": "View the history versions of the current chapter and restore content to a certain version", "tour.editor.markdown": "Switch between Markdown writing mode and visual mode", "tour.editor.preview": "The full preview button allows creators to preview the actual document reading effect before publishing", "editor.code.choose_language": "Choose Code Highlighting Language", "editor.code.language_identifier": "Language Identifier", "editor.code.language_description": "Enable syntax highlighting by providing the language identifier for your code block. For example: ruby, cpp, markdown, haskell, etc.", "editor.code.auto": "Auto", "editor.code.other": "Other", "language.zh_cn": "Simplified Chinese", "assistant.back": "Back", "assistant.close": "Close", "assistant.input_placeholder": "Please enter your task", "assistant.untitled_conversation": "Untitled Conversation", "assistant.delete_conversation": "Delete Conversation", "assistant.delete_conversation_confirm": "Are you sure you want to delete this conversation? This action cannot be undone.", "assistant.files_changed": "files changed", "assistant.discard_file_changes": "Discard changes to this file", "assistant.diff_load_error": "Unable to load file differences", "assistant.diff_deleted": "Deleted", "assistant.window.history": "History", "assistant.window.settings": "Settings", "assistant.window.chat": "Cha<PERSON>", "assistant.settings_developing": "Settings feature is under development...", "assistant.tool.cancelled": "Cancelled", "assistant.tool.parameters": "Parameters", "assistant.tool.response": "Response", "assistant.auth.title": "Authorize ThinkAI", "assistant.auth.token_placeholder": "Please enter ThinkAI token", "assistant.auth.save": "Save", "assistant.auth.cancel": "Cancel", "assistant.auth.button": "Authorize ThinkAI", "assistant.auth.input_disabled": "Please authorize ThinkAI token first"}