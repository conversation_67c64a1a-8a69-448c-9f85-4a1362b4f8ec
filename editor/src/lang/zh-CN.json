{"header.untitled": "未命名", "body.reload": "文档目录结构有变化，需重新加载文档！", "active-bar.catalog": "目录", "active-bar.files": "文件", "active-bar.theme": "主题", "active-bar.extension": "扩展", "active-bar.commit": "提交", "active-bar.commit.change": "变更", "active-bar.history": "历史", "active-bar.release": "发布", "active-bar.settings": "管理", "active-bar.settings.readme": "文档概要", "active-bar.settings.config": "文档设置", "active-bar.settings.style": "样式设置", "active-bar.settings.config.base": "基本设置", "active-bar.settings.config.base.language": "语言", "active-bar.settings.config.structure": "路径设置", "active-bar.settings.config.structure.root": "文档根目录", "active-bar.settings.config.structure.root.placeholder": "填写文档根目录相对于仓库根目录的路径", "active-bar.settings.config.structure.root.description": "设置文档在git仓库中的路径，默认留空为仓库的根目录，非必要请勿设置", "active-bar.settings.config.pdf": "PDF设置", "active-bar.settings.config.pdf.font_family": "字体", "active-bar.settings.config.pdf.margin": "页边距（单位: pt）", "active-bar.settings.config.pdf.margin.right": "右", "active-bar.settings.config.pdf.margin.left": "左", "active-bar.settings.config.pdf.margin.top": "上", "active-bar.settings.config.pdf.margin.bottom": "下", "active-bar.settings.config.pdf.paper_size": "纸张大小", "active-bar.settings.config.pdf.chapter_mark": "章节标记", "active-bar.settings.config.pdf.page_breaks_before": "在指定元素前插入分页符，XPath表达式，\"/\" 表示禁用", "active-bar.settings.config.pdf.page_numbers": "显示页码", "active-bar.settings.config.release": "发布设置", "active-bar.settings.config.release.path_encode": "是否对章节路径进行编码(默认会将中文转拼音)", "active-bar.files.search": "搜索", "active-bar.files.search.empty": "没有搜索到相关文件", "active-bar.files.replace": "替换", "side-bar.untitled": "未命名分组", "side-bar.rename": "重命名", "side-bar.delete": "删除", "side-bar.new_name": "新文件名", "side-bar.plugin.extension": "已安装", "side-bar.plugin.extension.title": "推荐扩展", "side-bar.plugin.extension.placeholder": "搜索扩展", "side-bar.plugin.theme": "当前主题", "side-bar.plugin.theme.preview": "预览", "side-bar.plugin.theme.title": "推荐主题", "side-bar.plugin.theme.placeholder": "搜索主题", "status-bar.sync": "同步更改", "status-bar.preview": "全文预览", "status-bar.feedback": "在线客服", "catalog.new_part": "新建分组", "catalog.edit_part": "编辑分组", "catalog.new_article": "新建章节", "catalog.import_article": "导入章节", "catalog.import_desc": "点击下方格式进行导入，导入前建议先提交所有的更改，便于取消本次导入", "catalog.import_failed": "导入失败", "catalog.import_as_single": "导入成单篇章节", "catalog.import_start": "开始导入", "catalog.import_cancel": "取消导入", "catalog.import_website": "静态站点", "catalog.import_website_series": "VitePress、docsify等", "catalog.import_website.placeholder": "请输入网页地址", "catalog.import_website.message": "支持VitePress、docsify、Docusaurus等静态站点生成器的网页", "catalog.import_independent": "独立目录", "catalog.import_independent.message": "开启后本次导入的文件将置于独立的目录中，避免和现有的文件发生冲突", "catalog.edit_article": "编辑章节", "catalog.article_title": "章节名称", "catalog.article_ref": "章节路径", "catalog.edit": "编辑", "catalog.part_name": "分组名称", "confirm.delete": "确定要删除吗？", "modal.ok": "确定", "modal.apply": "应用", "modal.confirm": "确认", "modal.cancel": "取消", "modal.insert": "插入", "modal.select": "选择", "modal.close": "关闭", "commit.empty": "无变更内容", "commit.reset_all": "放弃所有更改", "commit.reset": "放弃更改", "commit.reset_title": "放弃对 {filename} 的更改吗？", "commit.reset_message": "您将丢失对此文件所做的所有更改。此操作无法撤消。", "commit.reset_all_title": "放弃所有更改?", "commit.reset_all_message": "您将丢弃在此项目中所做的所有未提交的更改，此操作无法撤消。", "commit.message": "提交说明", "commit.release": "创建发布任务", "commit.hide": "收起", "commit.submit": "提交", "commit.submit_with_release": "提交并发布", "commit.releasing": "有正在发布中的任务", "diff.deleted": "已删除", "editor.placeholder": "请输入内容……", "editor.markdown.error": "markdown格式解析失败", "editor.markdown.switch": "切换 Markdown 模式编辑", "editor.tool.heading1": "标题一", "editor.tool.heading2": "标题二", "editor.tool.heading3": "标题三", "editor.tool.heading4": "标题四", "editor.tool.strong": "加粗", "editor.tool.emphasis": "斜体", "editor.tool.delete": "删除线", "editor.tool.code": "代码", "editor.tool.format": "格式化", "editor.tool.align.left": "左对齐", "editor.tool.align.center": "居中对齐", "editor.tool.align.right": "右对齐", "editor.tool.list.ordered": "有序列表", "editor.tool.list.bullet": "无序列表", "editor.tool.list.task": "任务列表", "editor.tool.link": "超链接", "editor.tool.link.insert": "插入链接", "editor.tool.link.edit": "设置链接", "editor.tool.link.url": "链接", "editor.tool.link.url.placeholder": "输入章节标题或外部链接", "editor.tool.link.text": "文本", "editor.tool.link.text.placeholder": "添加描述", "editor.tool.table": "表格", "editor.tool.asset.upload": "上传", "editor.tool.asset.browse": "浏览", "editor.tool.asset.browse.title": "文件管理", "editor.tool.image": "图片", "editor.tool.image.conflict": "检测到存在同名的图片文件,是否覆盖？点击“取消”则自动重命名", "editor.tool.image.invalid_name": "文件名不合规", "editor.tool.image.only_image": "只能上传图片文件", "editor.tool.image.placeholder": "请输入图片地址", "editor.tool.image.title": "插入图片", "editor.tool.image.preview": "图片预览区域", "editor.tool.video": "视频", "editor.tool.video.title": "插入视频", "editor.tool.video.placeholder": "请输入视频地址", "editor.tool.audio": "音频", "editor.tool.audio.title": "插入音频", "editor.tool.audio.placeholder": "请输入音频地址", "editor.tool.attachment": "附件", "editor.tool.attachment.title": "插入附件", "editor.tool.attachment.placeholder": "请输入附件地址", "editor.tool.attachment.name": "文件名", "editor.tool.attachment.size": "文件大小", "editor.tool.thematic-break": "分割线", "editor.tool.blockquote": "引用", "editor.tool.markdown": "源代码模式", "editor.tool.fullscreen": "全屏", "editor.tool.history": "历史", "editor.tool.history.redo": "还原", "editor.tool.history.undo": "撤销", "editor.tool.history.title": "修改历史", "editor.tool.history.revert": "恢复至此版本", "editor.tool.history.revert.confirm": "确定要使用该版本的内容替换当前编辑区的内容吗？", "editor.tool.history.saved": "文档已自动保存", "editor.toolbar.edit": "编辑", "editor.toolbar.delete": "删除", "editor.toolbar.table.insert_row": "增加一行", "editor.toolbar.table.insert_column": "增加一列", "editor.toolbar.table.remove_row": "删除当前行", "editor.toolbar.table.remove_column": "删除当前列", "editor.toolbar.image.original": "原始大小", "editor.toolbar.image.full": "铺满整行", "editor.toolbar.link.open": "打开外链", "editor.menu.delete": "删除", "editor.menu.tooltip": "点击显示菜单", "editor.workspace.binary.message": "此文件是二进制文件或使用了不支持的文本编码，无法在编辑器中显示。", "editor.workspace.binary.confirm": "是否仍要打开？", "editor.frontmatter.title": "网页标题", "editor.frontmatter.keywords": "关键词", "editor.frontmatter.description": "网页描述", "release.empty": "暂无发布日志", "release.message": "发布说明", "release.new": "发布文档", "release.pending": "队列中", "release.running": "执行中", "release.succeed": "成功", "release.failed": "失败", "release.unknown": "未知", "release.retry": "重试", "release.pack": "打包", "release.download": "下载", "release.denied": "无权限", "data.empty": "暂无数据", "plugin.readme": "简介", "plugin.install": "安装", "plugin.uninstall": "卸载", "plugin.uninstall.theme": "主题插件无法卸载，请安装其他主题插件替换", "plugin.reload": "需要重新加载", "plugin.trial.button": "试用", "plugin.trial.message": "试用中", "plugin.buy.button": "购买", "plugin.buy.result.title": "支付结果", "plugin.buy.result.message": "请在新打开的页面完成支付", "plugin.buy.result.ok": "支付成功", "plugin.buy.result.error": "尚未支付完成或支付结果尚未返回，请稍后重试", "plugin.disabled": "此插件尚未授权，已被禁用", "plugin.trailing": "此插件试用中，于{date}后到期", "plugin.expired": "此插件试用期已过，请购买授权", "plugin.theme": "主题", "plugin.extension": "扩展", "plugin.paid": "付费", "plugin.theme.setting.title": "主题设置", "plugin.theme.setting.common": "通用设置", "plugin.theme.setting.common.description": "通用设置为系统提供的基础功能设置，不同的主题支持程度不同", "plugin.theme.setting.default_mode": "默认模式", "plugin.theme.setting.default_mode.light": "浅色", "plugin.theme.setting.default_mode.dark": "深色", "plugin.theme.setting.primary_color": "主题色", "plugin.theme.setting.expand_level": "默认展开目录层级", "plugin.theme.setting.expand_level.description": "0表示不展开，1表示展开第一级，以此类推。-1表示展开全部", "plugin.theme.setting.navs": "导航设置", "plugin.theme.setting.navs.title": "文字", "plugin.theme.setting.navs.url": "链接", "plugin.theme.setting.navs.url.description": "外链请以http开头，内链可直接填写文件名，如：default.md", "plugin.theme.setting.current": "当前主题", "plugin.extension.setting.title": "插件设置", "merge-tool.header": "需要你解决一些冲突", "merge-tool.title": "{nums} 个冲突文件待解决", "merge-tool.empty": "请选择左边文件解决冲突", "merge-tool.theirs": "最终版本(基于他人版本)", "merge-tool.ours": "我的版本", "merge-tool.pick": "选用", "merge-tool.reset": "还原", "merge-tool.save": "保存", "merge-tool.use_theirs": "确定将最终版本还原为\"他人版本\"吗？", "merge-tool.use_ours": "确定使用\"我的版本\"作为最终版本吗？", "diff-viewer.collapse": "此差异已折叠。", "diff-viewer.expand": "点击以展开。", "theme.dark": "深色", "theme.light": "浅色", "theme.auto": "跟随系统", "tour.app.catalog": "首次编辑需要先创建章节然后进行内容创作，或者通过创建分组关联其它文档到当前文档", "tour.app.commit": "编辑完内容后可以点击提交栏的<strong>提交</strong>按钮同步修改内容，或选择<strong>提交并发布</strong>就可以同时发布，文章内容修改后需要发布之后才能更新阅读", "tour.app.plugin": "如果需要更改阅读主题或安装其它功能插件，可以点击扩展栏安装，安装插件或主题后需要重新发布才能生效", "tour.editor.tools": "通过顶部工具栏可以完成各种内容创作", "tour.editor.history": "查看当前章节的历史版本，可还原内容至某一版本", "tour.editor.markdown": "切换Markdown写作模式或可视化模式", "tour.editor.preview": "通过全文预览按钮可以让创作者在发布之前实际预览文档阅读效果", "editor.code.choose_language": "选择代码高亮语言", "editor.code.language_identifier": "语言标识符", "editor.code.language_description": "通过提供代码块语言的标识符来启用语法高亮。例如 ruby、cpp、markdown、haskell 等。", "editor.code.auto": "自动", "editor.code.other": "其他", "language.zh_cn": "简体中文", "language.en": "English", "assistant.new": "新的对话", "assistant.history": "历史对话", "assistant.back": "返回", "assistant.close": "关闭", "assistant.input_placeholder": "请输入你的任务", "assistant.untitled_conversation": "未命名对话", "assistant.delete_conversation": "删除对话", "assistant.delete_conversation_confirm": "确定要删除这个对话吗？删除后无法恢复。", "assistant.files_changed": "个文件已更改", "assistant.discard_file_changes": "放弃此文件的变更", "assistant.diff_load_error": "无法加载文件差异", "assistant.diff_deleted": "删除", "assistant.window.history": "历史记录", "assistant.window.settings": "设置", "assistant.window.chat": "聊天", "assistant.settings_developing": "设置功能开发中...", "assistant.tool.cancelled": "已取消", "assistant.tool.parameters": "参数", "assistant.tool.response": "响应", "assistant.auth.title": "授权 ThinkAI", "assistant.auth.token_placeholder": "请输入 ThinkAI 令牌", "assistant.auth.save": "保存", "assistant.auth.cancel": "取消", "assistant.auth.button": "授权 ThinkAI", "assistant.auth.input_disabled": "请先授权 ThinkAI 令牌"}