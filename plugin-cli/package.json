{"name": "@topwrite/plugin-cli", "version": "1.0.97", "description": "CLI for compiling plugins", "scripts": {"build": "rollup -c --environment NODE_ENV:production", "build:dev": "rollup -c", "watch": "rollup -c -w", "prepack": "npm run build"}, "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "files": ["bin", "public", "template/**/*", "topwrite"], "dependencies": {"@babel/runtime": "^7.11.2", "@topthink/webpack-config-plugin": "^1.0.16", "commander": "^8.1.0", "consola": "^2.15.3", "form-data": "^4.0.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.3.2", "inquirer": "^8.0.0", "json-schema-to-typescript": "^10.1.3", "libnpmpack": "^5.0.6", "libnpmpublish": "^7.0.6", "libnpmversion": "^4.0.1", "nanoid": "^3.1.25", "node-fetch": "^2.6.7", "pacote": "^15.0.7", "path-browserify": "^1.0.1", "process": "^0.11.10", "semver": "^7.3.8", "webpack": "^5.36.2", "webpack-dev-server": "^4.7.3", "webpack-merge": "^5.7.3"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.11.5", "@npm/types": "^1.0.2", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.0", "@types/cross-spawn": "^6.0.2", "@types/fs-extra": "^9.0.1", "@types/inquirer": "^7.3.0", "@types/libnpmpublish": "^4.0.3", "@types/node": "^16.4.13", "@types/node-fetch": "^2.5.7", "@types/pacote": "^11.1.5", "@types/react": "^18", "@types/semver": "^7.3.13", "configstore": "^6.0.0", "rollup": "^2.26.11", "rollup-plugin-preserve-shebangs": "^0.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.1", "topwrite": "^1.0.97", "typescript": "^5.3.3"}, "bin": {"ttwp": "./bin/ttwp.js"}}